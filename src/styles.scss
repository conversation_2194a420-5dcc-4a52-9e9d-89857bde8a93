/* TailwindCSS imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #1e293b;
  background-color: #ffffff;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.dark body {
  color: #f1f5f9;
  background-color: #0f172a;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom utility classes */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-800 font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
}

.btn-outline {
  @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.card {
  @apply bg-white dark:bg-secondary-800 rounded-xl shadow-lg border border-secondary-200 dark:border-secondary-700 p-6 transition-all duration-300 hover:shadow-xl dark:hover:shadow-2xl hover:scale-105;
}

.section-padding {
  @apply py-16 lg:py-24;
}

.text-gradient {
  @apply bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent;
}

/* Animation utilities */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animate {
  opacity: 1;
  transform: translateY(0);
}

.stagger-animation {
  animation-delay: var(--animation-delay, 0s);
}

/* Hover effects */
.hover-lift {
  @apply transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

.hover-glow {
  @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25;
}

/* Glass morphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
}

.dark .glass {
  @apply bg-black/10 border-white/10;
}
