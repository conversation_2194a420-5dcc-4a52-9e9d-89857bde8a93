<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-800 dark:via-primary-900 dark:to-secondary-900 text-white overflow-hidden transition-all duration-500">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0 bg-white bg-opacity-5"></div>
  </div>

  <div class="container-custom relative z-10 py-20 lg:py-32">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Hero Content -->
      <div class="text-center lg:text-left animate-fade-in-up">
        <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
          Professional
          <span class="text-gradient bg-gradient-to-r from-accent-400 to-accent-300 bg-clip-text text-transparent animate-glow">
            Chartered Accountant
          </span>
          Services
        </h1>

        <p class="text-xl lg:text-2xl text-primary-100 mb-8 leading-relaxed">
          {{ tagline }} - Providing expert financial solutions for individuals and businesses with over 15 years of experience.
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12 animate-fade-in-up" style="animation-delay: 0.3s;">
          <a routerLink="/contact" class="btn-primary text-lg px-8 py-4 hover-lift hover-glow">
            Get Free Consultation
          </a>
          <a routerLink="/services" class="btn-outline border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-4 hover-lift">
            Our Services
          </a>
        </div>

        <!-- Trust Indicators -->
        <div class="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-primary-200">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            Certified CA
          </div>
          <div class="flex items-center">
            <svg class="w-5 h-5 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            15+ Years Experience
          </div>
          <div class="flex items-center">
            <svg class="w-5 h-5 text-accent-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            500+ Happy Clients
          </div>
        </div>
      </div>

      <!-- Hero Image/Illustration -->
      <div class="relative animate-fade-in-right">
        <div class="glass p-8 hover-lift">
          <div class="grid grid-cols-2 gap-4">
            <div *ngFor="let stat of heroStats; let i = index"
                 class="text-center p-4 bg-white/10 dark:bg-white/5 rounded-lg hover-lift animate-scale-in"
                 [style.animation-delay]="(0.6 + i * 0.1) + 's'">
              <div class="text-2xl lg:text-3xl font-bold text-accent-400 animate-pulse-slow">{{ stat.number }}</div>
              <div class="text-sm text-primary-200">{{ stat.label }}</div>
            </div>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute -top-4 -right-4 w-20 h-20 bg-accent-400 rounded-full opacity-20 animate-float"></div>
        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-accent-300 rounded-full opacity-30 animate-float" style="animation-delay: 1s;"></div>
        <div class="absolute top-1/2 -left-8 w-12 h-12 bg-primary-400 rounded-full opacity-15 animate-float" style="animation-delay: 2s;"></div>
      </div>
    </div>
  </div>
</section>

<!-- Services Section -->
<section class="section-padding bg-secondary-50 dark:bg-secondary-900 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Our Core Services
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        Comprehensive financial solutions tailored to meet your business and personal needs
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div *ngFor="let service of coreServices; let i = index"
           class="card group animate-fade-in-up"
           [style.animation-delay]="(0.2 + i * 0.1) + 's'">
        <div class="text-center">
          <div class="text-4xl mb-4 animate-bounce-slow group-hover:animate-float">{{ service.icon }}</div>
          <h3 class="text-xl font-semibold text-secondary-800 dark:text-white mb-3 transition-colors duration-300">{{ service.title }}</h3>
          <p class="text-secondary-600 dark:text-secondary-300 mb-4 transition-colors duration-300">{{ service.description }}</p>

          <ul class="text-sm text-secondary-500 dark:text-secondary-400 space-y-1 mb-6 transition-colors duration-300">
            <li *ngFor="let feature of service.features" class="flex items-center">
              <svg class="w-4 h-4 text-primary-600 dark:text-primary-400 mr-2 flex-shrink-0 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              {{ feature }}
            </li>
          </ul>

          <a routerLink="/services" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium group-hover:underline transition-colors duration-200">
            Learn More →
          </a>
        </div>
      </div>
    </div>

    <div class="text-center mt-12">
      <a routerLink="/services" class="btn-primary">
        View All Services
      </a>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="section-padding bg-white">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
      <div>
        <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 mb-6">
          Why Choose {{ companyName }}?
        </h2>
        <p class="text-xl text-secondary-600 mb-8">
          We combine expertise, technology, and personalized service to deliver exceptional results for our clients.
        </p>

        <div class="space-y-6">
          <div class="flex items-start">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-secondary-800 mb-2">Certified Expertise</h3>
              <p class="text-secondary-600">Licensed Chartered Accountants with extensive experience in tax, audit, and business advisory services.</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-secondary-800 mb-2">Technology-Driven</h3>
              <p class="text-secondary-600">Modern tools and software to ensure accuracy, efficiency, and real-time reporting for all your financial needs.</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-secondary-800 mb-2">Personalized Service</h3>
              <p class="text-secondary-600">Dedicated account managers who understand your business and provide customized solutions for your unique requirements.</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-secondary-800 mb-2">Proven Track Record</h3>
              <p class="text-secondary-600">Over 15 years of successful client relationships with a 98% satisfaction rate and numerous success stories.</p>
            </div>
          </div>
        </div>
      </div>

      <div class="relative">
        <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-8">
          <div class="grid grid-cols-2 gap-6">
            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
              <div class="text-2xl font-bold text-primary-600">500+</div>
              <div class="text-sm text-secondary-600">Happy Clients</div>
            </div>
            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
              <div class="text-2xl font-bold text-primary-600">15+</div>
              <div class="text-sm text-secondary-600">Years Experience</div>
            </div>
            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
              <div class="text-2xl font-bold text-primary-600">1000+</div>
              <div class="text-sm text-secondary-600">Tax Returns</div>
            </div>
            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
              <div class="text-2xl font-bold text-primary-600">98%</div>
              <div class="text-sm text-secondary-600">Satisfaction</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="section-padding bg-secondary-50">
  <div class="container-custom">
    <div class="text-center mb-16">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 mb-4">
        What Our Clients Say
      </h2>
      <p class="text-xl text-secondary-600 max-w-3xl mx-auto">
        Don't just take our word for it - hear from our satisfied clients about their experience with our services
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div *ngFor="let testimonial of testimonials" class="card">
        <div class="flex items-center mb-4">
          <div class="flex text-accent-400">
            <svg *ngFor="let star of [1,2,3,4,5]" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
        </div>

        <blockquote class="text-secondary-600 mb-6 italic">
          "{{ testimonial.text }}"
        </blockquote>

        <div class="flex items-center">
          <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
            <span class="text-primary-600 font-semibold">{{ testimonial.name.charAt(0) }}</span>
          </div>
          <div>
            <div class="font-semibold text-secondary-800">{{ testimonial.name }}</div>
            <div class="text-sm text-secondary-500">{{ testimonial.company }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-12">
      <a routerLink="/about" class="btn-outline">
        Read More Reviews
      </a>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-gradient-to-r from-primary-600 to-primary-700 text-white">
  <div class="container-custom">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl lg:text-4xl font-bold mb-6">
        Ready to Take Control of Your Finances?
      </h2>
      <p class="text-xl text-primary-100 mb-8">
        Get expert financial advice and professional CA services. Schedule your free consultation today and discover how we can help you achieve your financial goals.
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a routerLink="/contact" class="bg-white text-primary-600 hover:bg-primary-50 font-medium py-4 px-8 rounded-lg transition-colors duration-200 text-lg">
          Schedule Free Consultation
        </a>
        <a routerLink="/services" class="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-4 px-8 rounded-lg transition-all duration-200 text-lg">
          Explore Our Services
        </a>
      </div>

      <div class="mt-8 flex flex-wrap justify-center gap-8 text-primary-200">
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
          </svg>
          Call: +91-9876543210
        </div>
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
          </svg>
          Email: info&#64;excellenceca.com
        </div>
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
          </svg>
          Mumbai, Maharashtra
        </div>
      </div>
    </div>
  </div>
</section>